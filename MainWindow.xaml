<Window x:Class="PEMTestSystem.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:PEMTestSystem"
        xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
        xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        mc:Ignorable="d"
        Title="PEM 电解槽自动化测试系统" Height="800" Width="1400"
        WindowState="Maximized" 
        Background="#1a1c23"
        Foreground="#c0c2c9"
        FontFamily="Roboto, Microsoft YaHei"
        FontSize="14">
    <Window.Resources>
        <!-- 颜色定义 -->
        <SolidColorBrush x:Key="BgColor" Color="#1a1c23"/>
        <SolidColorBrush x:Key="PanelColor" Color="#242731"/>
        <SolidColorBrush x:Key="BorderColor" Color="#3a3f51"/>
        <SolidColorBrush x:Key="PrimaryColor" Color="#00aaff"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#e1e1e1"/>
        <SolidColorBrush x:Key="TextColor" Color="#c0c2c9"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#00ff9b"/>
        <SolidColorBrush x:Key="DangerColor" Color="#ff4d4d"/>
        <SolidColorBrush x:Key="DisabledBgColor" Color="#444444"/>
        <SolidColorBrush x:Key="DisabledFgColor" Color="#888888"/>

        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarThumb" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Border x:Name="ThumbBorder" 
                                Background="{StaticResource PrimaryColor}" 
                                CornerRadius="3" 
                                Opacity="0.7"/>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ThumbBorder" Property="Opacity" Value="1"/>
                            </Trigger>
                            <Trigger Property="IsDragging" Value="True">
                                <Setter TargetName="ThumbBorder" Property="Background" Value="{StaticResource SuccessColor}"/>
                                <Setter TargetName="ThumbBorder" Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CustomScrollBar" TargetType="{x:Type ScrollBar}">
            <Setter Property="Background" Value="{StaticResource BorderColor}"/>
            <Setter Property="Width" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Border Background="{TemplateBinding Background}" CornerRadius="6">
                            <Track x:Name="PART_Track" IsDirectionReversed="True" Margin="2">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumb}"/>
                                </Track.Thumb>
                                <Track.IncreaseRepeatButton>
                                    <RepeatButton Command="ScrollBar.PageDownCommand" Opacity="0" Focusable="False"/>
                                </Track.IncreaseRepeatButton>
                                <Track.DecreaseRepeatButton>
                                    <RepeatButton Command="ScrollBar.PageUpCommand" Opacity="0" Focusable="False"/>
                                </Track.DecreaseRepeatButton>
                            </Track>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="Orientation" Value="Horizontal">
                    <Setter Property="Width" Value="Auto"/>
                    <Setter Property="Height" Value="12"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="{x:Type ScrollBar}">
                                <Border Background="{TemplateBinding Background}" CornerRadius="6">
                                    <Track x:Name="PART_Track" IsDirectionReversed="False" Margin="2">
                                        <Track.Thumb>
                                            <Thumb Style="{StaticResource CustomScrollBarThumb}"/>
                                        </Track.Thumb>
                                        <Track.IncreaseRepeatButton>
                                            <RepeatButton Command="ScrollBar.PageRightCommand" Opacity="0" Focusable="False"/>
                                        </Track.IncreaseRepeatButton>
                                        <Track.DecreaseRepeatButton>
                                            <RepeatButton Command="ScrollBar.PageLeftCommand" Opacity="0" Focusable="False"/>
                                        </Track.DecreaseRepeatButton>
                                    </Track>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="CustomScrollViewer" TargetType="{x:Type ScrollViewer}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0" 
                                                  Content="{TemplateBinding Content}" 
                                                  ContentTemplate="{TemplateBinding ContentTemplate}"/>
                            <ScrollBar Grid.Column="1" Grid.Row="0" 
                                     x:Name="PART_VerticalScrollBar"
                                     Style="{StaticResource CustomScrollBar}"
                                     Orientation="Vertical"
                                     Value="{TemplateBinding VerticalOffset}"
                                     Maximum="{TemplateBinding ScrollableHeight}"
                                     ViewportSize="{TemplateBinding ViewportHeight}"
                                     Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"/>
                            <ScrollBar Grid.Column="0" Grid.Row="1" 
                                     x:Name="PART_HorizontalScrollBar"
                                     Style="{StaticResource CustomScrollBar}"
                                     Orientation="Horizontal"
                                     Value="{TemplateBinding HorizontalOffset}"
                                     Maximum="{TemplateBinding ScrollableWidth}"
                                     ViewportSize="{TemplateBinding ViewportWidth}"
                                     Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 转换器 -->
        <local:BoolToStrikethroughConverter x:Key="BoolToStrikethroughConverter"/>
        <local:BoolToButtonContentConverter x:Key="BoolToButtonContentConverter"/>
        <local:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>

        <!-- 基础按钮样式 -->
        <Style TargetType="Button" x:Key="BaseButton">
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="{StaticResource DisabledBgColor}"/>
                                <Setter Property="Foreground" Value="{StaticResource DisabledFgColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 特定按钮样式 -->
        <Style TargetType="Button" x:Key="StartButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
            <Setter Property="Foreground" Value="#000"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Height" Value="50"/>
        </Style>
        <Style TargetType="Button" x:Key="StopButton" BasedOn="{StaticResource BaseButton}">
            <Setter Property="Background" Value="{StaticResource DangerColor}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Height" Value="50"/>
        </Style>
        <Style TargetType="Button" x:Key="SecondaryButton">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,3"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource SecondaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 卡片样式 -->
        <Style TargetType="Border" x:Key="CardStyle">
            <Setter Property="Background" Value="{StaticResource PanelColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
        </Style>
        <Style TargetType="TextBlock" x:Key="CardTitleStyle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
        </Style>
        <Style TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource BgColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="Foreground" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CaretBrush" Value="{StaticResource PrimaryColor}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border x:Name="border" CornerRadius="4" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="PART_ContentHost"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsKeyboardFocused" Value="True">
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Background" Value="#FF2A2A2A"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="#FF404040"/>
                                <Setter Property="Foreground" Value="#FF808080"/>
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- TabControl 样式 -->
        <Style TargetType="TabControl" x:Key="MainTabControlStyle">
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabControl">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <TabPanel Grid.Row="0" IsItemsHost="True" Background="Transparent" Margin="0,0,0,-1"/>
                            <Border Grid.Row="1" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,1,0,0">
                                <ContentPresenter ContentSource="SelectedContent"/>
                            </Border>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style TargetType="TabItem" x:Key="MainTabItemStyle">
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border x:Name="border" BorderBrush="Transparent" BorderThickness="0,0,0,2" Margin="0,0,10,0">
                            <ContentPresenter ContentSource="Header"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                                <Setter TargetName="border" Property="BorderBrush" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Foreground" Value="{StaticResource PrimaryColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 设备状态行样式 -->
        <Style x:Key="DeviceRowStyle" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="CornerRadius" Value="4"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource BorderColor}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 设备状态指示灯样式 -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
            <Setter Property="Width" Value="14"/>
            <Setter Property="Height" Value="14"/>
            <Setter Property="Fill" Value="{StaticResource DisabledFgColor}"/>
            <Style.Triggers>
                <!-- 绿色：设备正常运行 -->
                <DataTrigger Binding="{Binding Status}" Value="Online">
                    <Setter Property="Fill" Value="{StaticResource SuccessColor}"/>
                </DataTrigger>
                <!-- 红色：设备异常/故障 -->
                <DataTrigger Binding="{Binding Status}" Value="Error">
                    <Setter Property="Fill" Value="{StaticResource DangerColor}"/>
                </DataTrigger>
                <!-- 灰色：设备离线/未连接 -->
                <DataTrigger Binding="{Binding Status}" Value="Offline">
                    <Setter Property="Fill" Value="{StaticResource DisabledFgColor}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 设备名称标签样式 -->
        <Style x:Key="DeviceNameStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="Offline">
                    <Setter Property="Foreground" Value="{StaticResource DisabledFgColor}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 悬停按钮样式 -->
        <Style x:Key="HoverButtonStyle" TargetType="Button" BasedOn="{StaticResource SecondaryButton}">
            <Setter Property="Visibility" Value="Collapsed"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="6,3"/>
            <Setter Property="Margin" Value="3,0"/>
        </Style>

    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Border Grid.Row="0" Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal" Margin="20,10">
                <Path Data="M50,38 A12,12 0 1 1 50,62 A12,12 0 1 1 50,38 Z M50,5 A45,18 0 0 1 50,95 A45,18 0 0 1 50,5 Z" Stroke="{StaticResource PrimaryColor}" StrokeThickness="5" Fill="{StaticResource PrimaryColor}" Width="32" Height="32" Stretch="Uniform" RenderTransformOrigin="0.5,0.5">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <RotateTransform Angle="30"/>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
                <Path Data="M50,5 A45,18 0 0 1 50,95 A45,18 0 0 1 50,5 Z" Stroke="{StaticResource PrimaryColor}" StrokeThickness="5" Fill="Transparent" Width="32" Height="32" Stretch="Uniform" RenderTransformOrigin="0.5,0.5" Margin="-32,0,0,0">
                    <Path.RenderTransform>
                        <TransformGroup>
                            <RotateTransform Angle="-30"/>
                        </TransformGroup>
                    </Path.RenderTransform>
                </Path>
                <TextBlock Text="PEM 电解槽自动化测试系统" VerticalAlignment="Center" FontSize="20" FontWeight="SemiBold" Foreground="{StaticResource SecondaryColor}" Margin="15,0,0,0"/>
            </StackPanel>
        </Border>
        <Grid Grid.Row="1" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="380"/>
            </Grid.ColumnDefinitions>
            <DockPanel Grid.Column="0" >
                <!-- 实时数据监控面板 - 固定在顶部 -->
                <Border DockPanel.Dock="Top" Style="{StaticResource CardStyle}" Margin="0,0,0,15">
                    <DockPanel>
                        <DockPanel DockPanel.Dock="Top">
                            <TextBlock Text="实时数据监控" Style="{StaticResource CardTitleStyle}" DockPanel.Dock="Left"/>
                        </DockPanel>
                        <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>
                        <!-- 使用 Grid 布局实现均匀分布 -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 电解槽电压 -->
                            <StackPanel Grid.Column="0" Margin="0,0,10,10" HorizontalAlignment="Center">
                                <TextBlock Text="电解槽电压" Foreground="{StaticResource TextColor}" FontSize="15" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="VoltageValueText" Text="-- V" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- 电解槽电流 -->
                            <StackPanel Grid.Column="1" Margin="10,0,10,10" HorizontalAlignment="Center">
                                <TextBlock Text="电解槽电流" Foreground="{StaticResource TextColor}" FontSize="15" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="CurrentValueText" Text="-- A" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- 当前温度 -->
                            <StackPanel Grid.Column="2" Margin="10,0,10,10" HorizontalAlignment="Center">
                                <TextBlock Text="当前温度" Foreground="{StaticResource TextColor}" FontSize="15" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="TemperatureValueText" Text="-- °C" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- 流量泵 1 流量 -->
                            <StackPanel Grid.Column="3" Margin="10,0,10,10" HorizontalAlignment="Center">
                                <TextBlock Text="流量泵 1 流量" Foreground="{StaticResource TextColor}" FontSize="15" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="FlowRate1ValueText" Text="-- mL/min" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <!-- 流量泵 2 流量 -->
                            <StackPanel Grid.Column="4" Margin="10,0,0,10" HorizontalAlignment="Center">
                                <TextBlock Text="流量泵 2 流量" Foreground="{StaticResource TextColor}" FontSize="15" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="FlowRate2ValueText" Text="-- mL/min" Foreground="{StaticResource SecondaryColor}" FontSize="26" FontWeight="SemiBold" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </DockPanel>
                </Border>

                <!-- 实时曲线图面板 - 填充剩余空间 -->
                <Border Style="{StaticResource CardStyle}" Margin="0,0,0,15">
                    <DockPanel>
                        <Grid DockPanel.Dock="Top" Margin="0,0,0,15">
                            <TextBlock x:Name="ChartTitleText" Text="恒流模式: 电压-时间 (V-t) 曲线" 
                                       Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center">
                                <TextBlock x:Name="UnlimitedBadge" Text="无限制时间" Foreground="{StaticResource PrimaryColor}"
                                           Margin="0,0,10,0" Visibility="Collapsed"/>
                                <Button Content="导出" Style="{StaticResource SecondaryButton}" Margin="0,0,5,0"
                                        Click="ExportChart_Click"/>
                                <Button Content="历史记录" Style="{StaticResource SecondaryButton}"
                                        Click="ShowHistory_Click"/>
                            </StackPanel>
                        </Grid>

                        <!-- LiveCharts 图表区域 -->
                        <lvc:CartesianChart x:Name="RealTimeChart" 
                                            Series="{Binding ChartSeries}"
                                            AxisX="{Binding XAxis}"
                                            AxisY="{Binding YAxis}"
                                            Background="{StaticResource BgColor}"
                                            Foreground="{StaticResource TextColor}"
                                            AnimationsSpeed="0:0:0.5"
                                            Hoverable="False"
                                            DisableAnimations="False">
                            <lvc:CartesianChart.Resources>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
                                    <Setter Property="FontSize" Value="12"/>
                                </Style>
                            </lvc:CartesianChart.Resources>
                        </lvc:CartesianChart>
                    </DockPanel>
                </Border>
            </DockPanel>
            <DockPanel Grid.Column="2">
                <Border DockPanel.Dock="Bottom" Background="{StaticResource PanelColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="1" CornerRadius="8" Padding="20" Margin="0,15,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button x:Name="StartButton" Grid.Column="0" Content="开始实验" 
                                Style="{StaticResource StartButton}" Click="StartButton_Click" 
                                Height="50"/>
                        <Button x:Name="StopButton" Grid.Column="2" Content="停止实验" 
                                Style="{StaticResource StopButton}" IsEnabled="False" Click="StopButton_Click"
                                Height="50"/>
                    </Grid>
                </Border>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 设备状态面板 - 固定高度 -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,15">
                        <DockPanel>
                            <Grid DockPanel.Dock="Top">
                                <TextBlock Text="设备状态" Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                                <Button Content="设置" Style="{StaticResource SecondaryButton}" HorizontalAlignment="Right" VerticalAlignment="Center" ToolTip="打开设备连接设置" Click="DeviceSettingsButton_Click"/>
                            </Grid>
                            <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>
                            <ItemsControl ItemsSource="{Binding Devices}" BorderThickness="0" Background="Transparent">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate DataType="{x:Type local:DeviceStatus}">
                                        <Grid Margin="0,0,0,12">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <StackPanel Orientation="Horizontal" Grid.Column="0" VerticalAlignment="Center">
                                                <Ellipse Width="10" Height="10" VerticalAlignment="Center">
                                                    <Ellipse.Style>
                                                        <Style TargetType="Ellipse">
                                                            <Setter Property="Fill" Value="{StaticResource DangerColor}"/>
                                                            <Style.Triggers>
                                                                <MultiDataTrigger>
                                                                    <MultiDataTrigger.Conditions>
                                                                        <Condition Binding="{Binding IsEnabled}" Value="True"/>
                                                                        <Condition Binding="{Binding IsConnected}" Value="True"/>
                                                                    </MultiDataTrigger.Conditions>
                                                                    <Setter Property="Fill" Value="{StaticResource SuccessColor}"/>
                                                                </MultiDataTrigger>
                                                                <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                    <Setter Property="Fill" Value="{StaticResource DisabledFgColor}"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Ellipse.Style>
                                                </Ellipse>
                                                <TextBlock Text="{Binding Name}" Margin="10,0,0,0" VerticalAlignment="Center">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="TextDecorations" Value="{x:Null}"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                    <Setter Property="TextDecorations" Value="Strikethrough"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </StackPanel>

                                            <!-- 设置按钮 -->
                                            <Button Grid.Column="1" HorizontalAlignment="Center" Margin="5,0"
                                                    Content="设置" Width="50" Height="24"
                                                    Click="DeviceSettingsButton_Click"
                                                    Tag="{Binding Name}"
                                                    ToolTip="设备设置"
                                                    Style="{StaticResource SecondaryButton}">
                                            </Button>

                                            <!-- 启用/禁用按钮 -->
                                            <Button Grid.Column="2" HorizontalAlignment="Center" Margin="5,0"
                                                    Content="{Binding IsEnabled, Converter={StaticResource BoolToButtonContentConverter}}"
                                                    Visibility="{Binding CanBeDisabled, Converter={StaticResource BoolToVisibilityConverter}}"
                                                    Click="ToggleDeviceState_Click"
                                                    Style="{StaticResource SecondaryButton}">
                                            </Button>

                                            <TextBlock Grid.Column="3" HorizontalAlignment="Right" VerticalAlignment="Center">
                                                <TextBlock.Style>
                                                    <Style TargetType="TextBlock">
                                                        <Setter Property="Text" Value="连接异常"/>
                                                        <Setter Property="Foreground" Value="{StaticResource DangerColor}"/>
                                                        <Style.Triggers>
                                                            <MultiDataTrigger>
                                                                <MultiDataTrigger.Conditions>
                                                                    <Condition Binding="{Binding IsEnabled}" Value="True"/>
                                                                    <Condition Binding="{Binding IsConnected}" Value="True"/>
                                                                </MultiDataTrigger.Conditions>
                                                                <Setter Property="Text" Value="已连接"/>
                                                                <Setter Property="Foreground" Value="{StaticResource SuccessColor}"/>
                                                            </MultiDataTrigger>
                                                            <DataTrigger Binding="{Binding IsEnabled}" Value="False">
                                                                <Setter Property="Text" Value="已禁用"/>
                                                                <Setter Property="Foreground" Value="{StaticResource DisabledFgColor}"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </TextBlock.Style>
                                            </TextBlock>
                                        </Grid>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </DockPanel>
                    </Border>

                    <!-- 设备状态面板 -->
                    <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="0,0,0,15" VerticalAlignment="Bottom">
                        <DockPanel>
                            <Grid DockPanel.Dock="Top">
                                <TextBlock Text="设备状态" Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                            </Grid>
                            <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,15"/>

                            <!-- 设备状态列表 -->
                            <StackPanel>
                                <!-- 电源设备行 -->
                                <Border x:Name="PowerDeviceRow" Style="{StaticResource DeviceRowStyle}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 状态指示灯 -->
                                        <Ellipse Grid.Column="0" x:Name="PowerStatusIndicator"
                                                Style="{StaticResource StatusIndicatorStyle}"
                                                VerticalAlignment="Center" Margin="0,0,10,0"/>

                                        <!-- 设备名称 -->
                                        <TextBlock Grid.Column="1" Text="电源"
                                                  Style="{StaticResource DeviceNameStyle}"/>

                                        <!-- 设置按钮 (悬停显示) -->
                                        <Button Grid.Column="3" x:Name="PowerSettingsButton"
                                               Content="设置" Style="{StaticResource HoverButtonStyle}"
                                               Click="PowerSettingsButton_Click"/>
                                    </Grid>
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="MouseEnter">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PowerSettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="MouseLeave">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="PowerSettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>

                                <!-- 温控器设备行 -->
                                <Border x:Name="TemperatureControllerRow" Style="{StaticResource DeviceRowStyle}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 状态指示灯 -->
                                        <Ellipse Grid.Column="0" x:Name="TemperatureStatusIndicator"
                                                Style="{StaticResource StatusIndicatorStyle}"
                                                VerticalAlignment="Center" Margin="0,0,10,0"/>

                                        <!-- 设备名称 -->
                                        <TextBlock Grid.Column="1" Text="温控器"
                                                  Style="{StaticResource DeviceNameStyle}"/>

                                        <!-- 禁用按钮 (悬停显示) -->
                                        <Button Grid.Column="2" x:Name="TemperatureDisableButton"
                                               Content="禁用" Style="{StaticResource HoverButtonStyle}"
                                               Click="TemperatureDisableButton_Click"/>

                                        <!-- 设置按钮 (悬停显示) -->
                                        <Button Grid.Column="3" x:Name="TemperatureSettingsButton"
                                               Content="设置" Style="{StaticResource HoverButtonStyle}"
                                               Click="TemperatureSettingsButton_Click"/>
                                    </Grid>
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="MouseEnter">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TemperatureDisableButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TemperatureSettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="MouseLeave">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TemperatureDisableButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="TemperatureSettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>

                                <!-- 流量泵1设备行 -->
                                <Border x:Name="FlowPump1Row" Style="{StaticResource DeviceRowStyle}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 状态指示灯 -->
                                        <Ellipse Grid.Column="0" x:Name="FlowPump1StatusIndicator"
                                                Style="{StaticResource StatusIndicatorStyle}"
                                                VerticalAlignment="Center" Margin="0,0,10,0"/>

                                        <!-- 设备名称 -->
                                        <TextBlock Grid.Column="1" Text="流量泵1"
                                                  Style="{StaticResource DeviceNameStyle}"/>

                                        <!-- 禁用按钮 (悬停显示) -->
                                        <Button Grid.Column="2" x:Name="FlowPump1DisableButton"
                                               Content="禁用" Style="{StaticResource HoverButtonStyle}"
                                               Click="FlowPump1DisableButton_Click"/>

                                        <!-- 设置按钮 (悬停显示) -->
                                        <Button Grid.Column="3" x:Name="FlowPump1SettingsButton"
                                               Content="设置" Style="{StaticResource HoverButtonStyle}"
                                               Click="FlowPump1SettingsButton_Click"/>
                                    </Grid>
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="MouseEnter">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump1DisableButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump1SettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="MouseLeave">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump1DisableButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump1SettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>

                                <!-- 流量泵2设备行 -->
                                <Border x:Name="FlowPump2Row" Style="{StaticResource DeviceRowStyle}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 状态指示灯 -->
                                        <Ellipse Grid.Column="0" x:Name="FlowPump2StatusIndicator"
                                                Style="{StaticResource StatusIndicatorStyle}"
                                                VerticalAlignment="Center" Margin="0,0,10,0"/>

                                        <!-- 设备名称 -->
                                        <TextBlock Grid.Column="1" Text="流量泵2"
                                                  Style="{StaticResource DeviceNameStyle}"/>

                                        <!-- 禁用按钮 (悬停显示) -->
                                        <Button Grid.Column="2" x:Name="FlowPump2DisableButton"
                                               Content="禁用" Style="{StaticResource HoverButtonStyle}"
                                               Click="FlowPump2DisableButton_Click"/>

                                        <!-- 设置按钮 (悬停显示) -->
                                        <Button Grid.Column="3" x:Name="FlowPump2SettingsButton"
                                               Content="设置" Style="{StaticResource HoverButtonStyle}"
                                               Click="FlowPump2SettingsButton_Click"/>
                                    </Grid>
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="MouseEnter">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump2DisableButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump2SettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="MouseLeave">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump2DisableButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="FlowPump2SettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>

                                <!-- 数据库设备行 -->
                                <Border x:Name="DatabaseRow" Style="{StaticResource DeviceRowStyle}">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- 状态指示灯 -->
                                        <Ellipse Grid.Column="0" x:Name="DatabaseStatusIndicator"
                                                Style="{StaticResource StatusIndicatorStyle}"
                                                VerticalAlignment="Center" Margin="0,0,10,0"/>

                                        <!-- 设备名称 -->
                                        <TextBlock Grid.Column="1" Text="数据库"
                                                  Style="{StaticResource DeviceNameStyle}"/>

                                        <!-- 设置按钮 (悬停显示) -->
                                        <Button Grid.Column="3" x:Name="DatabaseSettingsButton"
                                               Content="设置" Style="{StaticResource HoverButtonStyle}"
                                               Click="DatabaseSettingsButton_Click"/>
                                    </Grid>
                                    <Border.Triggers>
                                        <EventTrigger RoutedEvent="MouseEnter">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DatabaseSettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Visible}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                        <EventTrigger RoutedEvent="MouseLeave">
                                            <BeginStoryboard>
                                                <Storyboard>
                                                    <ObjectAnimationUsingKeyFrames Storyboard.TargetName="DatabaseSettingsButton"
                                                                                 Storyboard.TargetProperty="Visibility">
                                                        <DiscreteObjectKeyFrame KeyTime="0" Value="{x:Static Visibility.Collapsed}"/>
                                                    </ObjectAnimationUsingKeyFrames>
                                                </Storyboard>
                                            </BeginStoryboard>
                                        </EventTrigger>
                                    </Border.Triggers>
                                </Border>
                            </StackPanel>
                        </DockPanel>
                    </Border>

                    <!-- 实验模式与参数设置面板 - 使用剩余空间并支持滚动 -->
                    <Border Grid.Row="1" Style="{StaticResource CardStyle}">
                        <DockPanel>
                            <Grid DockPanel.Dock="Top">
                                <TextBlock Text="实验模式与参数设置" Style="{StaticResource CardTitleStyle}" VerticalAlignment="Center"/>
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                    <Button Content="选择" Style="{StaticResource SecondaryButton}" Margin="0,0,5,0"
                                            Click="SelectConfigButton_Click" ToolTip="选择实验配置文件"/>
                                    <Button Content="保存" Style="{StaticResource SecondaryButton}"
                                            Click="SaveConfigButton_Click" ToolTip="保存当前实验配置"/>
                                </StackPanel>
                            </Grid>
                            <Separator DockPanel.Dock="Top" Background="{StaticResource BorderColor}" Margin="0,10,0,10"/>
                            <TextBlock x:Name="ConfigFileNameText" DockPanel.Dock="Top" Text="配置文件: 未加载配置文件" FontSize="14" Foreground="{StaticResource PrimaryColor}" Margin="0,0,0,15"/>

                            <!-- 关键修改：添加自定义滚动条样式和鼠标滚轮支持 -->
                            <ScrollViewer VerticalScrollBarVisibility="Auto"
                                          HorizontalScrollBarVisibility="Disabled"
                                          Style="{StaticResource CustomScrollViewer}"
                                          PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                                <TabControl x:Name="ExperimentModeTabControl" 
                                            Style="{StaticResource MainTabControlStyle}" 
                                            ItemContainerStyle="{StaticResource MainTabItemStyle}"
                                            SelectionChanged="ExperimentModeTabControl_SelectionChanged">
                                    <TabItem Header="恒流" Tag="ConstantCurrent">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      Style="{StaticResource CustomScrollViewer}"
                                                      PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                                            <StackPanel Margin="0,15,0,0">
                                                <TextBlock Text="通用参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标温度 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentTargetTemperatureBox" Text="80" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：室温~90°C" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="流量泵1流量 (mL/min)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentFlowPump1Box" Text="1.25" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 mL/min" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="流量泵2流量 (mL/min)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentFlowPump2Box" Text="0.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 mL/min" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="重复次数" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentRepeatCountBox" Text="1" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：1~999" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="采样间隔 (秒)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentSamplingIntervalBox" Text="1" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0.1~60秒" TextChanged="ParameterTextBox_TextChanged"/>
                                                </Grid>
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="恒定电流模式特有参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标电流 (A)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentTargetCurrentBox" Text="20.00" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~170A，精度：0.01A" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="实验持续时间 (秒)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Vertical" VerticalAlignment="Center">
                                                        <TextBox x:Name="ConstantCurrentDurationBox" Text="3600" Margin="5" ToolTip="范围：1~999999秒" TextChanged="ParameterTextBox_TextChanged"/>
                                                        <CheckBox x:Name="ConstantCurrentUnlimitedCheckBox" Content="无限制" Margin="5,0,5,5" Foreground="White" Checked="UnlimitedDuration_Checked" Unchecked="UnlimitedDuration_Unchecked"/>
                                                    </StackPanel>
                                                </Grid>

                                                <!-- 安全参数设置区域 -->
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="安全保护参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="温度保护阈值 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentTemperatureThresholdBox" Text="95.0" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：20~95°C，超过此温度将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电压安全上限 (V)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentVoltageMaxBox" Text="10.0" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，超过此电压将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电压安全下限 (V)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentVoltageMinBox" Text="0.0" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，低于此电压将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电流安全上限 (A)" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentCurrentMaxBox" Text="200.0" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：0~200A，超过此电流将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电流安全下限 (A)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantCurrentCurrentMinBox" Text="0.0" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0~200A，低于此电流将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                </Grid>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </TabItem>
                                    <TabItem Header="恒压" Tag="ConstantVoltage">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      Style="{StaticResource CustomScrollViewer}"
                                                      PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                                            <StackPanel Margin="0,15,0,0">
                                                <TextBlock Text="通用参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标温度 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageTargetTemperatureBox" Text="80" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：室温~90°C" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="流量泵1流量 (mL/min)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageFlowPump1Box" Text="1.25" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 mL/min" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="流量泵2流量 (mL/min)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageFlowPump2Box" Text="0.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 mL/min" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="重复次数" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageRepeatCountBox" Text="1" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：1~999" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="采样间隔 (秒)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageSamplingIntervalBox" Text="1" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0.1~60秒" TextChanged="ParameterTextBox_TextChanged"/>
                                                </Grid>
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="恒定电压模式特有参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标电压 (V)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageTargetVoltageBox" Text="1.800" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="实验持续时间 (秒)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <StackPanel Grid.Row="1" Grid.Column="1" Orientation="Vertical" VerticalAlignment="Center">
                                                        <TextBox x:Name="ConstantVoltageDurationBox" Text="3600" Margin="5" ToolTip="范围：1~999999秒" TextChanged="ParameterTextBox_TextChanged"/>
                                                        <CheckBox x:Name="ConstantVoltageUnlimitedCheckBox" Content="无限制" Margin="5,0,5,5" Foreground="White" Checked="UnlimitedDuration_Checked" Unchecked="UnlimitedDuration_Unchecked"/>
                                                    </StackPanel>
                                                </Grid>

                                                <!-- 安全参数设置区域 -->
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="安全保护参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="温度保护阈值 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageTemperatureThresholdBox" Text="95.0" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：20~95°C，超过此温度将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电压安全上限 (V)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageVoltageMaxBox" Text="10.0" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，超过此电压将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电压安全下限 (V)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageVoltageMinBox" Text="0.0" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，低于此电压将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电流安全上限 (A)" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageCurrentMaxBox" Text="200.0" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：0~200A，超过此电流将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电流安全下限 (A)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="ConstantVoltageCurrentMinBox" Text="0.0" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0~200A，低于此电流将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                </Grid>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </TabItem>
                                    <TabItem Header="线扫" Tag="LinearScan">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                                      Style="{StaticResource CustomScrollViewer}"
                                                      PreviewMouseWheel="ScrollViewer_PreviewMouseWheel">
                                            <StackPanel Margin="0,15,0,0">
                                                <TextBlock Text="通用参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="目标温度 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanTargetTemperatureBox" Text="80" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：室温~90°C" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="流量泵1流量 (mL/min)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanFlowPump1Box" Text="1.25" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 mL/min" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="流量泵2流量 (mL/min)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanFlowPump2Box" Text="0.00" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0.1~400 mL/min" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="重复次数" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanRepeatCountBox" Text="1" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：1~999" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="采样间隔 (秒)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanSamplingIntervalBox" Text="1" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0.1~60秒" TextChanged="ParameterTextBox_TextChanged"/>
                                                </Grid>
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="线性提升电压模式特有参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="起始电压 (V)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanStartVoltageBox" Text="1.200" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="终点电压 (V)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanEndVoltageBox" Text="2.200" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，精度：0.001V" TextChanged="ParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="变化方式" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                                        <RadioButton x:Name="ScanByRateRadio" Content="斜率" IsChecked="True" GroupName="ScanMethod" Checked="ScanMethod_Checked" Foreground="White"/>
                                                        <RadioButton x:Name="ScanByTimeRadio" Content="时间" GroupName="ScanMethod" Margin="10,0,0,0" Checked="ScanMethod_Checked" Foreground="White"/>
                                                    </StackPanel>
                                                </Grid>
                                                <Grid x:Name="ScanRateGroup" Margin="0,5,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Text="变化斜率 (V/s)" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanRateBox" Grid.Column="1" Text="0.01" Margin="5" ToolTip="与变化时间二选一" TextChanged="ParameterTextBox_TextChanged"/>
                                                </Grid>
                                                <Grid x:Name="ScanTimeGroup" Visibility="Collapsed" Margin="0,5,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <TextBlock Text="变化时间 (秒)" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanTimeBox" Grid.Column="1" Text="100" Margin="5" ToolTip="与变化斜率二选一" TextChanged="ParameterTextBox_TextChanged"/>
                                                </Grid>
                                                <Grid Margin="0,10,0,0">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="终点保持时间 (秒)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanHoldTimeBox" Text="60" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="到达终点电压后的保持时间" TextChanged="ParameterTextBox_TextChanged"/>
                                                </Grid>

                                                <!-- 安全参数设置区域 -->
                                                <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>
                                                <TextBlock Text="安全保护参数" FontWeight="SemiBold" Margin="0,0,0,10"/>
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Grid.RowDefinitions>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                        <RowDefinition/>
                                                    </Grid.RowDefinitions>
                                                    <TextBlock Text="温度保护阈值 (°C)" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanTemperatureThresholdBox" Text="95.0" Grid.Row="0" Grid.Column="1" Margin="5" ToolTip="范围：20~95°C，超过此温度将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电压安全上限 (V)" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanVoltageMaxBox" Text="10.0" Grid.Row="1" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，超过此电压将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电压安全下限 (V)" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanVoltageMinBox" Text="0.0" Grid.Row="2" Grid.Column="1" Margin="5" ToolTip="范围：0~10V，低于此电压将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电流安全上限 (A)" Grid.Row="3" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanCurrentMaxBox" Text="200.0" Grid.Row="3" Grid.Column="1" Margin="5" ToolTip="范围：0~200A，超过此电流将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="电流安全下限 (A)" Grid.Row="4" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanCurrentMinBox" Text="0.0" Grid.Row="4" Grid.Column="1" Margin="5" ToolTip="范围：0~200A，低于此电流将自动停止实验" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                    <TextBlock Text="最大扫描速率 (V/s)" Grid.Row="5" Grid.Column="0" VerticalAlignment="Center"/>
                                                    <TextBox x:Name="LinearScanMaxRateBox" Text="1.0" Grid.Row="5" Grid.Column="1" Margin="5" ToolTip="范围：0.001~1.0 V/s，扫描速率不能超过此值" TextChanged="SafetyParameterTextBox_TextChanged"/>
                                                </Grid>
                                            </StackPanel>
                                        </ScrollViewer>
                                    </TabItem>
                                </TabControl>
                            </ScrollViewer>
                        </DockPanel>
                    </Border>
                </Grid>
            </DockPanel>
        </Grid>
    </Grid>
</Window>