using System;
using PEMTestSystem.Models.System;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 安全参数功能测试
    /// </summary>
    public class SafetyParametersTest
    {
        /// <summary>
        /// 运行所有安全参数测试
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== 安全参数功能测试 ===");

            try
            {
                TestSafetyParametersManager();
                TestParameterValidation();
                TestExperimentModeParameters();
                TestNullReferenceProtection();

                Console.WriteLine("\n✅ 所有安全参数测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ 安全参数测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 测试null引用保护
        /// </summary>
        private static void TestNullReferenceProtection()
        {
            Console.WriteLine("\n4. 测试null引用保护");

            var manager = new SafetyParametersManager();

            // 测试空参数名称
            var result1 = manager.ValidateParameter("", 50.0, ExperimentMode.ConstantCurrent);
            Assert(!result1.IsValid, "空参数名称应该返回无效");

            var result2 = manager.ValidateParameter(null!, 50.0, ExperimentMode.ConstantCurrent);
            Assert(!result2.IsValid, "null参数名称应该返回无效");

            // 测试无效数值
            var result3 = manager.ValidateParameter("temperature", double.NaN, ExperimentMode.ConstantCurrent);
            Assert(!result3.IsValid, "NaN值应该返回无效");

            var result4 = manager.ValidateParameter("temperature", double.PositiveInfinity, ExperimentMode.ConstantCurrent);
            Assert(!result4.IsValid, "无穷大值应该返回无效");

            // 测试未知模式
            var unknownMode = (ExperimentMode)999;
            var result5 = manager.GetParametersForMode(unknownMode);
            Assert(result5 == null, "未知模式应该返回null");

            Console.WriteLine("   ✓ null引用保护功能正常");
        }

        /// <summary>
        /// 测试SafetyParametersManager基本功能
        /// </summary>
        private static void TestSafetyParametersManager()
        {
            Console.WriteLine("\n1. 测试SafetyParametersManager基本功能");
            
            var manager = new SafetyParametersManager();
            
            // 测试默认参数
            Assert(manager.GlobalParameters != null, "全局参数应该不为空");
            Assert(manager.ConstantCurrentParameters != null, "恒流参数应该不为空");
            Assert(manager.ConstantVoltageParameters != null, "恒压参数应该不为空");
            Assert(manager.LinearScanParameters != null, "线扫参数应该不为空");
            
            // 测试默认值
            Assert(manager.GlobalParameters.MaxTemperature == 95.0, "默认最大温度应为95°C");
            Assert(manager.GlobalParameters.MaxVoltage == 10.0, "默认最大电压应为10V");
            Assert(manager.GlobalParameters.MaxCurrent == 200.0, "默认最大电流应为200A");
            
            Console.WriteLine("   ✓ SafetyParametersManager基本功能正常");
        }

        /// <summary>
        /// 测试参数验证功能
        /// </summary>
        private static void TestParameterValidation()
        {
            Console.WriteLine("\n2. 测试参数验证功能");
            
            var manager = new SafetyParametersManager();
            
            // 测试温度验证
            var result1 = manager.ValidateParameter("temperature", 80.0, ExperimentMode.ConstantCurrent);
            Assert(result1.IsValid, "80°C应该是有效温度");
            
            var result2 = manager.ValidateParameter("temperature", 100.0, ExperimentMode.ConstantCurrent);
            Assert(!result2.IsValid, "100°C应该是无效温度");
            Assert(result2.ErrorMessage.Contains("温度"), "错误信息应包含温度相关内容");
            
            // 测试电压验证
            var result3 = manager.ValidateParameter("voltage", 5.0, ExperimentMode.ConstantVoltage);
            Assert(result3.IsValid, "5V应该是有效电压");
            
            var result4 = manager.ValidateParameter("voltage", 15.0, ExperimentMode.ConstantVoltage);
            Assert(!result4.IsValid, "15V应该是无效电压");
            
            // 测试电流验证
            var result5 = manager.ValidateParameter("current", 100.0, ExperimentMode.ConstantCurrent);
            Assert(result5.IsValid, "100A应该是有效电流");
            
            var result6 = manager.ValidateParameter("current", 250.0, ExperimentMode.ConstantCurrent);
            Assert(!result6.IsValid, "250A应该是无效电流");
            
            Console.WriteLine("   ✓ 参数验证功能正常");
        }

        /// <summary>
        /// 测试不同实验模式的参数获取
        /// </summary>
        private static void TestExperimentModeParameters()
        {
            Console.WriteLine("\n3. 测试不同实验模式的参数获取");
            
            var manager = new SafetyParametersManager();
            
            // 测试恒流模式
            var ccParams = manager.GetParametersForMode(ExperimentMode.ConstantCurrent);
            Assert(ccParams is ConstantCurrentSafetyParameters, "恒流模式应返回ConstantCurrentSafetyParameters");
            
            // 测试恒压模式
            var cvParams = manager.GetParametersForMode(ExperimentMode.ConstantVoltage);
            Assert(cvParams is ConstantVoltageSafetyParameters, "恒压模式应返回ConstantVoltageSafetyParameters");
            
            // 测试线扫模式
            var lsParams = manager.GetParametersForMode(ExperimentMode.LinearScan);
            Assert(lsParams is LinearScanSafetyParameters, "线扫模式应返回LinearScanSafetyParameters");
            
            // 测试线扫模式特有参数
            var linearParams = lsParams as LinearScanSafetyParameters;
            Assert(linearParams != null, "线扫参数转换应成功");
            Assert(linearParams!.MaxScanRate == 1.0, "默认最大扫描速率应为1.0 V/s");
            Assert(linearParams!.DefaultScanDuration == 120.0, "默认扫描持续时间应为120秒");
            
            Console.WriteLine("   ✓ 实验模式参数获取功能正常");
        }

        /// <summary>
        /// 简单的断言方法
        /// </summary>
        private static void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"断言失败: {message}");
            }
        }
    }
}
