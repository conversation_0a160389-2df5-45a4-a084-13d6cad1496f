using System;
using System.Diagnostics;
using System.Threading.Tasks;
using PEMTestSystem.Models.System;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 安全参数缓存性能测试
    /// 验证安全参数缓存机制的性能优化效果
    /// </summary>
    public class SafetyParametersCacheTest
    {
        /// <summary>
        /// 测试入口点
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== 安全参数缓存性能测试 ===");
            Console.WriteLine();

            try
            {
                // 测试1：模拟频繁的UI控件访问（优化前的方式）
                await TestFrequentUIAccess();

                // 测试2：模拟使用缓存的方式（优化后的方式）
                await TestCachedAccess();

                // 测试3：对比性能差异
                await ComparePerformance();

                Console.WriteLine("\n=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试频繁的UI控件访问（模拟优化前的方式）
        /// </summary>
        private static async Task TestFrequentUIAccess()
        {
            Console.WriteLine("1. 测试频繁UI控件访问（优化前方式）");
            
            var stopwatch = Stopwatch.StartNew();
            const int iterations = 10000;

            for (int i = 0; i < iterations; i++)
            {
                // 模拟每次都创建新的安全参数对象（类似于从UI控件读取）
                var parameters = CreateMockSafetyParameters();
                
                // 模拟安全检查逻辑
                var isValid = ValidateMockDataPoint(parameters);
                
                if (i % 1000 == 0)
                {
                    Console.Write(".");
                }
            }

            stopwatch.Stop();
            Console.WriteLine($"\n频繁UI访问方式: {iterations} 次检查耗时 {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"平均每次检查耗时: {(double)stopwatch.ElapsedMilliseconds / iterations:F4} ms");
        }

        /// <summary>
        /// 测试使用缓存的方式（模拟优化后的方式）
        /// </summary>
        private static async Task TestCachedAccess()
        {
            Console.WriteLine("\n2. 测试缓存访问方式（优化后方式）");
            
            // 预先创建缓存的安全参数（模拟在开始实验时缓存）
            var cachedParameters = CreateMockSafetyParameters();
            
            var stopwatch = Stopwatch.StartNew();
            const int iterations = 10000;

            for (int i = 0; i < iterations; i++)
            {
                // 直接使用缓存的参数，无需重新创建
                var isValid = ValidateMockDataPoint(cachedParameters);
                
                if (i % 1000 == 0)
                {
                    Console.Write(".");
                }
            }

            stopwatch.Stop();
            Console.WriteLine($"\n缓存访问方式: {iterations} 次检查耗时 {stopwatch.ElapsedMilliseconds} ms");
            Console.WriteLine($"平均每次检查耗时: {(double)stopwatch.ElapsedMilliseconds / iterations:F4} ms");
        }

        /// <summary>
        /// 对比性能差异
        /// </summary>
        private static async Task ComparePerformance()
        {
            Console.WriteLine("\n3. 性能对比测试");
            
            const int iterations = 50000;
            
            // 测试优化前方式
            var sw1 = Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                var parameters = CreateMockSafetyParameters();
                ValidateMockDataPoint(parameters);
            }
            sw1.Stop();
            
            // 测试优化后方式
            var cachedParameters = CreateMockSafetyParameters();
            var sw2 = Stopwatch.StartNew();
            for (int i = 0; i < iterations; i++)
            {
                ValidateMockDataPoint(cachedParameters);
            }
            sw2.Stop();
            
            Console.WriteLine($"优化前方式: {sw1.ElapsedMilliseconds} ms");
            Console.WriteLine($"优化后方式: {sw2.ElapsedMilliseconds} ms");
            Console.WriteLine($"性能提升: {(double)sw1.ElapsedMilliseconds / sw2.ElapsedMilliseconds:F2}x");
            Console.WriteLine($"时间节省: {sw1.ElapsedMilliseconds - sw2.ElapsedMilliseconds} ms ({(double)(sw1.ElapsedMilliseconds - sw2.ElapsedMilliseconds) / sw1.ElapsedMilliseconds * 100:F1}%)");
        }

        /// <summary>
        /// 创建模拟的安全参数（模拟从UI控件读取的开销）
        /// </summary>
        private static ExperimentSafetyParameters CreateMockSafetyParameters()
        {
            // 模拟从UI控件读取参数的开销
            System.Threading.Thread.Sleep(0); // 微小延迟模拟UI访问
            
            return new ConstantCurrentSafetyParameters
            {
                TemperatureThreshold = 95.0,
                VoltageUpperLimit = 10.0,
                VoltageLowerLimit = 0.0,
                CurrentUpperLimit = 200.0,
                CurrentLowerLimit = 0.0,
                SamplingInterval = 1.0
            };
        }

        /// <summary>
        /// 模拟数据点验证逻辑
        /// </summary>
        private static bool ValidateMockDataPoint(ExperimentSafetyParameters parameters)
        {
            // 模拟数据点
            double voltage = 5.0;
            double current = 100.0;
            double temperature = 75.0;
            
            // 模拟安全检查逻辑
            if (temperature > parameters.TemperatureThreshold) return false;
            if (voltage > parameters.VoltageUpperLimit || voltage < parameters.VoltageLowerLimit) return false;
            if (current > parameters.CurrentUpperLimit || current < parameters.CurrentLowerLimit) return false;
            
            return true;
        }
    }
}
