using System;
using System.ComponentModel.DataAnnotations;

namespace PEMTestSystem.Models.System
{
    /// <summary>
    /// 系统配置实体类
    /// </summary>
    public class SystemConfiguration
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string ConfigurationKey { get; set; } = string.Empty;

        public string? ConfigurationValue { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// 配置分类：Database, Device, Safety, UI
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// 数据类型：String, Integer, Decimal, Boolean, Json
        /// </summary>
        [Required]
        [MaxLength(20)]
        public string DataType { get; set; } = string.Empty;

        /// <summary>
        /// 是否加密存储
        /// </summary>
        public bool IsEncrypted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 全局安全参数配置
    /// </summary>
    public class GlobalSafetyParameters
    {
        /// <summary>
        /// 最大温度阈值 (°C)
        /// </summary>
        public double MaxTemperature { get; set; } = 95.0;

        /// <summary>
        /// 最小温度阈值 (°C)
        /// </summary>
        public double MinTemperature { get; set; } = 20.0;

        /// <summary>
        /// 最大电压限制 (V)
        /// </summary>
        public double MaxVoltage { get; set; } = 10.0;

        /// <summary>
        /// 最小电压限制 (V)
        /// </summary>
        public double MinVoltage { get; set; } = 0.0;

        /// <summary>
        /// 最大电流限制 (A)
        /// </summary>
        public double MaxCurrent { get; set; } = 200.0;

        /// <summary>
        /// 最小电流限制 (A)
        /// </summary>
        public double MinCurrent { get; set; } = 0.0;

        /// <summary>
        /// 紧急停止超时时间 (ms)
        /// </summary>
        public int EmergencyStopTimeout { get; set; } = 500;

        /// <summary>
        /// 设备通信超时时间 (s)
        /// </summary>
        public int DeviceCommunicationTimeout { get; set; } = 5;
    }

    /// <summary>
    /// 实验模式特定安全参数
    /// </summary>
    public class ExperimentSafetyParameters
    {
        /// <summary>
        /// 温度保护阈值 (°C)
        /// </summary>
        public double TemperatureThreshold { get; set; } = 95.0;

        /// <summary>
        /// 电压上限保护 (V)
        /// </summary>
        public double VoltageUpperLimit { get; set; } = 10.0;

        /// <summary>
        /// 电压下限保护 (V)
        /// </summary>
        public double VoltageLowerLimit { get; set; } = 0.0;

        /// <summary>
        /// 电流上限保护 (A)
        /// </summary>
        public double CurrentUpperLimit { get; set; } = 200.0;

        /// <summary>
        /// 电流下限保护 (A)
        /// </summary>
        public double CurrentLowerLimit { get; set; } = 0.0;

        /// <summary>
        /// 数据采集间隔 (s)
        /// </summary>
        public double SamplingInterval { get; set; } = 1.0;

        /// <summary>
        /// 最大实验持续时间 (s)
        /// </summary>
        public int MaxExperimentDuration { get; set; } = 999999;

        /// <summary>
        /// 最小实验持续时间 (s)
        /// </summary>
        public int MinExperimentDuration { get; set; } = 1;
    }

    /// <summary>
    /// 恒流模式安全参数
    /// </summary>
    public class ConstantCurrentSafetyParameters : ExperimentSafetyParameters
    {
        /// <summary>
        /// 目标电流安全范围上限 (A)
        /// </summary>
        public double TargetCurrentUpperLimit { get; set; } = 170.0;

        /// <summary>
        /// 目标电流安全范围下限 (A)
        /// </summary>
        public double TargetCurrentLowerLimit { get; set; } = 0.0;
    }

    /// <summary>
    /// 恒压模式安全参数
    /// </summary>
    public class ConstantVoltageSafetyParameters : ExperimentSafetyParameters
    {
        /// <summary>
        /// 目标电压安全范围上限 (V)
        /// </summary>
        public double TargetVoltageUpperLimit { get; set; } = 10.0;

        /// <summary>
        /// 目标电压安全范围下限 (V)
        /// </summary>
        public double TargetVoltageLowerLimit { get; set; } = 0.0;
    }

    /// <summary>
    /// 线扫模式安全参数
    /// </summary>
    public class LinearScanSafetyParameters : ExperimentSafetyParameters
    {
        /// <summary>
        /// 扫描电压范围上限 (V)
        /// </summary>
        public double ScanVoltageUpperLimit { get; set; } = 10.0;

        /// <summary>
        /// 扫描电压范围下限 (V)
        /// </summary>
        public double ScanVoltageLowerLimit { get; set; } = 0.0;

        /// <summary>
        /// 最大扫描速率 (V/s)
        /// </summary>
        public double MaxScanRate { get; set; } = 1.0;

        /// <summary>
        /// 最小扫描速率 (V/s)
        /// </summary>
        public double MinScanRate { get; set; } = 0.001;

        /// <summary>
        /// 最大扫描时间 (s)
        /// </summary>
        public int MaxScanTime { get; set; } = 86400; // 24小时

        /// <summary>
        /// 最小扫描时间 (s)
        /// </summary>
        public int MinScanTime { get; set; } = 1;

        /// <summary>
        /// 默认线扫持续时间 (s) - 当计算失败时使用
        /// </summary>
        public double DefaultScanDuration { get; set; } = 120.0;

        /// <summary>
        /// 最大终点保持时间 (s)
        /// </summary>
        public int MaxHoldTime { get; set; } = 86400; // 24小时

        /// <summary>
        /// 最小终点保持时间 (s)
        /// </summary>
        public int MinHoldTime { get; set; } = 0;
    }
}