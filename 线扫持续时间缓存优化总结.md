# 线扫持续时间缓存优化总结

## 问题描述

在原始代码中，`CheckLinearScanStopConditions` 方法在每次采样数据时都会被调用，导致其中的 `CalculateLinearScanDuration()` 方法也被重复调用多次，这造成了不必要的性能开销。

### 性能问题分析

- **采样频率**: 默认每秒采样一次 (`SamplingIntervalSeconds = 1.0`)
- **调用频率**: 每次数据采集都会触发 `OnDataPointReceived` 事件
- **重复计算**: 每次事件处理都会调用 `CalculateLinearScanDuration()` 方法
- **计算复杂度**: 该方法需要解析多个UI控件的文本值并进行数学计算

### 性能影响估算

假设一个典型的线扫实验：
- 实验持续时间：120秒
- 采样间隔：1秒
- 总数据点：120个

**优化前**：`CalculateLinearScanDuration()` 被调用 120 次
**优化后**：`CalculateLinearScanDuration()` 被调用 1 次

理论性能改进：**99.2%** 的计算开销减少

## 优化方案

### 1. 添加缓存字段

在 `MainWindow` 类中添加了以下字段：

```csharp
/// <summary>
/// 线扫模式的持续时间（秒）。在实验开始时计算一次并缓存，避免重复计算。
/// 仅在线扫模式下使用，其他模式下为0。
/// 使用锁保证线程安全。
/// </summary>
private double _linearScanDurationSeconds = 0;

/// <summary>
/// 用于保护 _linearScanDurationSeconds 字段的锁对象。
/// </summary>
private readonly object _linearScanDurationLock = new object();
```

### 2. 修改计算时机

将 `CalculateLinearScanDuration()` 的调用从 `CheckLinearScanStopConditions` 方法中移除，改为在"开始实验"按钮的点击事件处理器中调用一次：

```csharp
// 如果是线扫模式，预先计算并缓存持续时间
if (_currentMode == ExperimentMode.LinearScan)
{
    var calculatedDuration = CalculateLinearScanDuration();
    lock (_linearScanDurationLock)
    {
        _linearScanDurationSeconds = calculatedDuration;
        if (_linearScanDurationSeconds <= 0)
        {
            App.AlarmService.Warning("线扫参数", "无法计算线扫持续时间，使用默认值120秒");
            _linearScanDurationSeconds = 120; // 仅作为备用值
        }
    }
    App.AlarmService.Info("线扫计算", $"线扫持续时间已缓存: {calculatedDuration:F1}秒");
}
else
{
    lock (_linearScanDurationLock)
    {
        _linearScanDurationSeconds = 0; // 非线扫模式重置为0
    }
}
```

### 3. 修改使用方式

修改 `CheckLinearScanStopConditions` 方法，让它直接使用存储的计算结果：

```csharp
// 使用缓存的线扫持续时间，避免重复计算
double scanDurationSeconds;
lock (_linearScanDurationLock)
{
    scanDurationSeconds = _linearScanDurationSeconds;
}

if (scanDurationSeconds <= 0)
{
    App.AlarmService.Warning("线扫参数", "缓存的线扫持续时间无效，使用默认值120秒");
    scanDurationSeconds = 120; // 仅作为备用值
}
```

### 4. 确保线程安全

使用 `lock` 语句保护对 `_linearScanDurationSeconds` 字段的访问，确保在多线程环境下的安全性。

### 5. 状态清理

在实验停止时清理缓存值：

```csharp
// 清理线扫持续时间缓存
lock (_linearScanDurationLock)
{
    _linearScanDurationSeconds = 0;
}
```

## 优化效果

### 性能改进

1. **计算次数减少**: 从每秒1次减少到实验开始时1次
2. **CPU使用率降低**: 减少了重复的UI控件访问和数学计算
3. **响应性提升**: 数据采集过程中的停止条件检查更加高效

### 内存使用

- **增加**: 1个 `double` 字段 (8字节) + 1个锁对象
- **减少**: 避免了重复创建临时变量和字符串解析操作

### 代码质量

1. **可维护性**: 缓存逻辑集中管理，易于理解和维护
2. **线程安全**: 使用锁机制确保多线程环境下的安全性
3. **错误处理**: 保留了原有的错误处理和默认值机制

## 测试验证

### 编译测试

✅ 代码成功编译，无编译错误

### 功能测试

- ✅ 线扫模式参数计算正常
- ✅ 缓存机制工作正常
- ✅ 线程安全保护有效
- ✅ 状态清理正确执行

### 性能测试

创建了专门的性能测试类 `LinearScanDurationCacheTest.cs`，验证：
- 缓存性能改进效果
- 多线程环境下的线程安全性
- 缓存清理机制

## 兼容性

### 向后兼容

- ✅ 不影响现有功能
- ✅ 保持原有的错误处理逻辑
- ✅ 保持原有的日志记录

### 配置兼容

- ✅ 不需要修改配置文件
- ✅ 不影响用户界面
- ✅ 不改变实验参数设置方式

## 总结

这次优化成功解决了线扫模式下的性能问题：

1. **显著减少了CPU开销**: 避免了每秒重复计算线扫持续时间
2. **提升了系统响应性**: 数据采集过程更加流畅
3. **保持了代码质量**: 添加了适当的线程安全保护和错误处理
4. **确保了兼容性**: 不影响现有功能和用户体验

该优化特别适用于长时间运行的线扫实验，能够有效减少系统资源消耗，提升整体性能。
