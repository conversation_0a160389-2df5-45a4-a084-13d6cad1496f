using System;
using System.Threading.Tasks;
using System.Diagnostics;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 线扫持续时间缓存优化测试
    /// 验证 CalculateLinearScanDuration 方法的缓存机制是否正常工作
    /// </summary>
    public class LinearScanDurationCacheTest
    {
        /// <summary>
        /// 测试入口点
        /// </summary>
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== 线扫持续时间缓存优化测试 ===");
            Console.WriteLine();

            var test = new LinearScanDurationCacheTest();
            await test.RunAllTests();

            Console.WriteLine();
            Console.WriteLine("测试完成，按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task RunAllTests()
        {
            try
            {
                TestCachePerformanceImprovement();
                await TestThreadSafety();
                TestCacheCleanup();

                Console.WriteLine("✅ 所有测试通过！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }

        /// <summary>
        /// 测试缓存性能改进
        /// 模拟在数据采集过程中重复调用停止条件检查的场景
        /// </summary>
        private void TestCachePerformanceImprovement()
        {
            Console.WriteLine("🧪 测试1: 缓存性能改进");

            // 模拟参数
            const int simulatedDataPoints = 1000; // 模拟1000个数据点
            const double mockDuration = 120.0; // 模拟120秒的线扫持续时间

            // 测试未优化版本（每次都计算）
            var stopwatch = Stopwatch.StartNew();
            for (int i = 0; i < simulatedDataPoints; i++)
            {
                // 模拟原来的方式：每次都调用计算方法
                var duration = SimulateCalculateLinearScanDuration();
                var shouldStop = SimulateCheckStopCondition(i, duration);
            }
            stopwatch.Stop();
            var unoptimizedTime = stopwatch.ElapsedMilliseconds;

            // 测试优化版本（缓存一次）
            stopwatch.Restart();
            var cachedDuration = SimulateCalculateLinearScanDuration(); // 只计算一次
            for (int i = 0; i < simulatedDataPoints; i++)
            {
                // 模拟优化后的方式：使用缓存值
                var shouldStop = SimulateCheckStopCondition(i, cachedDuration);
            }
            stopwatch.Stop();
            var optimizedTime = stopwatch.ElapsedMilliseconds;

            // 计算性能改进
            var improvement = (double)(unoptimizedTime - optimizedTime) / unoptimizedTime * 100;

            Console.WriteLine($"   未优化版本耗时: {unoptimizedTime} ms");
            Console.WriteLine($"   优化版本耗时: {optimizedTime} ms");
            Console.WriteLine($"   性能改进: {improvement:F1}%");

            if (improvement > 0)
            {
                Console.WriteLine("   ✅ 性能优化有效");
            }
            else
            {
                Console.WriteLine("   ⚠️  性能改进不明显（可能是因为模拟计算太简单）");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试线程安全性
        /// 模拟多线程环境下的缓存访问
        /// </summary>
        private async Task TestThreadSafety()
        {
            Console.WriteLine("🧪 测试2: 线程安全性");

            const int threadCount = 10;
            const int operationsPerThread = 100;
            var tasks = new Task[threadCount];
            var lockObject = new object();
            double cachedValue = 0;
            bool hasError = false;

            // 模拟多线程访问缓存
            for (int i = 0; i < threadCount; i++)
            {
                int threadId = i;
                tasks[i] = Task.Run(() =>
                {
                    try
                    {
                        for (int j = 0; j < operationsPerThread; j++)
                        {
                            // 模拟读取缓存值
                            double value;
                            lock (lockObject)
                            {
                                value = cachedValue;
                            }

                            // 模拟写入缓存值（在实际代码中只在开始实验时发生）
                            if (j == 0)
                            {
                                lock (lockObject)
                                {
                                    cachedValue = SimulateCalculateLinearScanDuration();
                                }
                            }

                            // 模拟使用缓存值进行计算
                            var result = SimulateCheckStopCondition(j, value);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   线程 {threadId} 发生异常: {ex.Message}");
                        hasError = true;
                    }
                });
            }

            await Task.WhenAll(tasks);

            if (!hasError)
            {
                Console.WriteLine("   ✅ 线程安全测试通过");
            }
            else
            {
                Console.WriteLine("   ❌ 线程安全测试失败");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 测试缓存清理
        /// 验证实验停止后缓存值被正确清理
        /// </summary>
        private void TestCacheCleanup()
        {
            Console.WriteLine("🧪 测试3: 缓存清理");

            // 模拟实验开始时设置缓存
            double cachedDuration = SimulateCalculateLinearScanDuration();
            Console.WriteLine($"   实验开始，缓存持续时间: {cachedDuration:F1}秒");

            // 模拟实验停止时清理缓存
            cachedDuration = 0;
            Console.WriteLine($"   实验停止，清理缓存: {cachedDuration:F1}秒");

            if (cachedDuration == 0)
            {
                Console.WriteLine("   ✅ 缓存清理测试通过");
            }
            else
            {
                Console.WriteLine("   ❌ 缓存清理测试失败");
            }

            Console.WriteLine();
        }

        /// <summary>
        /// 模拟计算线扫持续时间（包含一些计算开销）
        /// </summary>
        private double SimulateCalculateLinearScanDuration()
        {
            // 模拟一些计算开销
            double result = 0;
            for (int i = 0; i < 1000; i++)
            {
                result += Math.Sin(i) * Math.Cos(i);
            }

            // 返回模拟的持续时间
            return 120.0 + (result % 10); // 120-130秒之间的值
        }

        /// <summary>
        /// 模拟检查停止条件
        /// </summary>
        private bool SimulateCheckStopCondition(int dataPointIndex, double duration)
        {
            // 模拟经过的时间（假设每个数据点代表1秒）
            double elapsedSeconds = dataPointIndex;
            return elapsedSeconds >= duration;
        }
    }
}
